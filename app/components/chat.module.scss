@import "../styles/animation.scss";

.attach-images {
  position: absolute;
  left: 30px;
  bottom: 32px;
  display: flex;
}

.attach-image {
  cursor: default;
  width: 64px;
  height: 64px;
  border: rgba($color: #888, $alpha: 0.2) 1px solid;
  border-radius: 5px;
  margin-right: 10px;
  background-size: cover;
  background-position: center;
  background-color: var(--white);

  .attach-image-mask {
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: all ease 0.2s;
  }

  .attach-image-mask:hover {
    opacity: 1;
  }

  .delete-image {
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    float: right;
    background-color: var(--white);
  }
}

.chat-input-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  gap: 5px;

  &-end {
    display: flex;
    margin-left: auto;
    gap: 5px;
    align-items: flex-start;
  }

  .model-selector-container {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 8px;
  }

  .model-selector-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 20px;
    border: var(--border-in-light);
    background-color: var(--white);
    color: var(--black);
    cursor: pointer;
    font-size: 12px;
    box-shadow: var(--card-shadow);
    animation: slide-in ease 0.3s;
    outline: none;
    height: 32px;
    justify-content: center;
    width: auto;
    min-width: fit-content;

    &:hover {
      background-color: var(--hover-color);
    }

    .model-icon {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      // 确保彩色提供商图标正常显示，不受暗夜模式影响
      svg, div {
        filter: none !important;
        opacity: 1;
      }
    }

    .model-name {
      white-space: nowrap;
      margin-left: 4px;
      font-weight: 500;
      color: var(--black);
      font-size: 13px;
      font-family: "Noto Sans", "SF Pro SC", "SF Pro Text", "SF Pro Icons",
        "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
    }
  }

  // 暗夜模式下确保彩色图标正常显示
  :global(.dark) & {
    .model-selector-button {
      .model-icon {
        // 彩色提供商图标在暗夜模式下保持原色，不需要额外处理
        svg, div {
          filter: none !important;
          opacity: 1;
        }
      }
    }
  }

  .chat-action-wrapper {
    position: relative;
    display: inline-block;
  }

  .chat-input-action {
    display: inline-flex;
    border-radius: 20px;
    font-size: 12px;
    background-color: var(--white);
    color: var(--black);
    border: var(--border-in-light);
    padding: 8px 12px;
    animation: slide-in ease 0.3s;
    box-shadow: var(--card-shadow);
    align-items: center;
    justify-content: center;
    height: 32px;
    min-width: 32px;
    cursor: pointer;
    outline: none;

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
    }

    &:hover {
      background-color: var(--hover-color);
    }

    &:focus {
      outline: 2px solid var(--primary);
      outline-offset: 1px;
    }
  }

  .token-counter-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius: 20px;
    border: var(--border-in-light);
    background-color: var(--white);
    color: var(--black);
    cursor: default;
    font-size: 12px;
    box-shadow: var(--card-shadow);
    animation: slide-in ease 0.3s;
    outline: none;
    height: 32px;
    justify-content: center;
    width: auto;
    min-width: fit-content;

    .token-counter-text {
      white-space: nowrap;
      font-weight: 500;
      color: var(--black);
      font-size: 13px;
      font-family: "Noto Sans", "SF Pro SC", "SF Pro Text", "SF Pro Icons",
        "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
      pointer-events: none;
    }

    &:hover {
      background-color: var(--hover-color);
    }

    &:focus {
      outline: none;
    }

    &:active {
      transform: none;
    }
  }

  .chat-action-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 8px;
    padding: 6px 10px;
    background-color: var(--white);
    color: var(--black);
    font-size: 12px;
    border-radius: 6px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
  }

  .token-counter-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: var(--white);
    color: var(--black);
    font-size: 12px;
    border-radius: 6px;
    border: var(--border-in-light);
    box-shadow: var(--card-shadow);
    z-index: 1000;
    pointer-events: none;
    white-space: normal;
    min-width: 200px;

    div {
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }


}

.prompt-toast {
  position: absolute;
  bottom: -50px;
  z-index: 999;
  display: flex;
  justify-content: center;
  width: calc(100% - 40px);

  .prompt-toast-inner {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    background-color: var(--white);
    color: var(--black);

    border: var(--border-in-light);
    box-shadow: var(--card-shadow);
    padding: 10px 20px;
    border-radius: 100px;

    animation: slide-in-from-top ease 0.3s;

    .prompt-toast-content {
      margin-left: 10px;
    }
  }
}

.section-title {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title-action {
    display: flex;
    align-items: center;
  }
}

.context-prompt {
  .context-prompt-insert {
    display: flex;
    justify-content: center;
    padding: 4px;
    opacity: 0.2;
    transition: all ease 0.3s;
    background-color: rgba(0, 0, 0, 0);
    cursor: pointer;
    border-radius: 4px;
    margin-top: 4px;
    margin-bottom: 4px;

    &:hover {
      opacity: 1;
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .context-prompt-row {
    display: flex;
    justify-content: center;
    width: 100%;

    &:hover {
      .context-drag {
        opacity: 1;
      }
    }

    .context-drag {
      display: flex;
      align-items: center;
      opacity: 0.5;
      transition: all ease 0.3s;
    }

    .context-role {
      margin-right: 10px;
    }

    .context-content {
      flex: 1;
      max-width: 100%;
      text-align: left;
    }

    .context-delete-button {
      margin-left: 10px;
    }
  }

  .context-prompt-button {
    flex: 1;
  }
}

.memory-prompt {
  margin: 20px 0;

  .memory-prompt-content {
    background-color: var(--white);
    color: var(--black);
    border: var(--border-in-light);
    border-radius: 10px;
    padding: 10px;
    font-size: 12px;
    user-select: text;
  }
}

.clear-context {
  margin: 20px 0 0 0;
  padding: 4px 0;

  border-top: var(--border-in-light);
  border-bottom: var(--border-in-light);
  box-shadow: var(--card-shadow) inset;

  display: flex;
  justify-content: center;
  align-items: center;

  color: var(--black);
  transition: all ease 0.3s;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  font-size: 12px;

  animation: slide-in ease 0.3s;

  $linear: linear-gradient(
    to right,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 1),
    rgba(0, 0, 0, 0)
  );
  mask-image: $linear;

  @mixin show {
    transform: translateY(0);
    position: relative;
    transition: all ease 0.3s;
    opacity: 1;
  }

  @mixin hide {
    transform: translateY(-50%);
    position: absolute;
    transition: all ease 0.1s;
    opacity: 0;
  }

  &-tips {
    @include show;
    opacity: 0.5;
  }

  &-revert-btn {
    color: var(--primary);
    @include hide;
  }

  &:hover {
    opacity: 1;
    border-color: var(--primary);

    .clear-context-tips {
      @include hide;
    }

    .clear-context-revert-btn {
      @include show;
    }
  }
}

.chat {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
}

.chat-body {
  flex: 1;
  overflow: auto;
  overflow-x: hidden;
  padding: 20px;
  padding-bottom: 40px;
  position: relative;
  overscroll-behavior: none;
}

.chat-body-main-title {
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

@media only screen and (max-width: 600px) {
  .chat-body-title {
    text-align: center;
  }
}

.chat-message {
  display: flex;
  flex-direction: row;

  &:last-child {
    animation: slide-in ease 0.3s;
  }
}

.chat-message-user {
  display: flex;
  flex-direction: row-reverse;

  .chat-message-header {
    flex-direction: row-reverse;
  }
}

.chat-message-header {
  margin-top: 20px;
  display: flex;
  align-items: center;

  .chat-message-actions {
    display: flex;
    box-sizing: border-box;
    font-size: 12px;
    align-items: flex-end;
    justify-content: space-between;
    transition: all ease 0.3s;
    transform: scale(0.9) translateY(5px);
    margin: 0 10px;
    opacity: 0;
    pointer-events: none;

    .chat-input-actions {
      display: flex;
      flex-wrap: nowrap;
    }
  }

  .chat-model-name {
    font-size: 12px;
    color: var(--black);
    margin-left: 6px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .chat-model-provider {
    font-size: 10px;
    background: var(--primary);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    opacity: 0.9;
  }
}

/* 多模型面板样式 */
.multi-model-description {
  margin-bottom: 16px;
  font-size: 14px;
  color: var(--black);
  opacity: 0.8;
  line-height: 1.4;
}

.multi-model-select-button {
  width: 100%;
  padding: 12px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;

  &:hover {
    background: var(--primary);
    filter: brightness(1.1);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.multi-model-select-icon {
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
}

.multi-model-selected-container {
  margin-top: 16px;
}

.multi-model-selected-title {
  font-size: 13px;
  color: var(--black);
  opacity: 0.7;
  margin-bottom: 12px;
  font-weight: 500;
}

.multi-model-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.multi-model-badge {
  display: flex;
  align-items: center;
  background: var(--hover-color);
  border: 1px solid var(--border-in-light);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  transition: all 0.2s ease;
  max-width: 100%;

  &:hover {
    background: var(--border-in-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.multi-model-badge-content {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.multi-model-badge-name {
  color: var(--black);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.multi-model-badge-provider {
  color: var(--primary);
  font-weight: 400;
  opacity: 0.8;
  white-space: nowrap;
}

.multi-model-badge-remove {
  margin-left: 8px;
  background: none;
  border: none;
  color: var(--black);
  opacity: 0.5;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    background: rgba(255, 0, 0, 0.1);
    color: #ff4444;
  }
}

.multi-model-tips {
  margin-top: 16px;
  padding: 12px;
  background: var(--hover-color);
  border: 1px solid var(--border-in-light);
  border-radius: 8px;
  font-size: 13px;
  color: var(--black);
  opacity: 0.8;
  line-height: 1.4;
}

.chat-assistant-name {
  margin-left: 8px;
  padding: 2px 8px;
  background: var(--primary);
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  opacity: 0.9;
}

.chat-message-container {
  max-width: var(--message-max-width);
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  &:hover {
    .chat-message-edit {
      opacity: 0.9;
    }

    .chat-message-actions {
      opacity: 1;
      pointer-events: all;
      transform: scale(1) translateY(0);
    }
  }
}

.chat-message-user > .chat-message-container {
  align-items: flex-end;
}

.chat-message-avatar {
  position: relative;

  .chat-message-edit {
    position: absolute;
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all ease 0.3s;

    button {
      padding: 7px;
    }
  }

  /* Specific styles for iOS devices */
  @media screen and (max-device-width: 812px) and (-webkit-min-device-pixel-ratio: 2) {
    @supports (-webkit-touch-callout: none) {
      .chat-message-edit {
        top: -8%;
      }
    }
  }
}

.chat-message-status {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
  line-height: 1.5;
  margin-top: 5px;
}

.chat-message-tools {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
  line-height: 1.5;
  margin-top: 5px;
  .chat-message-tool {
    display: flex;
    align-items: end;
    svg {
      margin-left: 5px;
      margin-right: 5px;
    }
  }
}

.chat-message-item {
  box-sizing: border-box;
  max-width: 100%;
  margin-top: 10px;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 10px;
  font-size: 14px;
  user-select: text;
  word-break: break-word;
  border: var(--border-in-light);
  position: relative;
  transition: all ease 0.3s;
}

.chat-message-audio {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  border: var(--border-in-light);
  position: relative;
  transition: all ease 0.3s;
  margin-top: 10px;
  font-size: 14px;
  user-select: text;
  word-break: break-word;
  box-sizing: border-box;
  audio {
    height: 30px; /* 调整高度 */
  }
}

.chat-message-item-image {
  width: 100%;
  margin-top: 10px;
}

.chat-message-item-images {
  width: 100%;
  display: grid;
  justify-content: left;
  grid-gap: 10px;
  grid-template-columns: repeat(var(--image-count), auto);
  margin-top: 10px;
}

.chat-message-item-image-multi {
  object-fit: cover;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.chat-message-item-image,
.chat-message-item-image-multi {
  box-sizing: border-box;
  border-radius: 10px;
  border: rgba($color: #888, $alpha: 0.2) 1px solid;
}

@media only screen and (max-width: 600px) {
  $calc-image-width: calc(100vw / 3 * 2 / var(--image-count));

  .chat-message-item-image-multi {
    width: $calc-image-width;
    height: $calc-image-width;
  }

  .chat-message-item-image {
    max-width: calc(100vw / 3 * 2);
  }
}

@media screen and (min-width: 600px) {
  $max-image-width: calc(
    calc(1200px - var(--sidebar-width)) / 3 * 2 / var(--image-count)
  );
  $image-width: calc(
    calc(var(--window-width) - var(--sidebar-width)) / 3 * 2 /
      var(--image-count)
  );

  .chat-message-item-image-multi {
    width: $image-width;
    height: $image-width;
    max-width: $max-image-width;
    max-height: $max-image-width;
  }

  .chat-message-item-image {
    max-width: calc(calc(1200px - var(--sidebar-width)) / 3 * 2);
  }
}

.chat-message-action-date {
  font-size: 12px;
  opacity: 0.2;
  white-space: nowrap;
  transition: all ease 0.6s;
  color: var(--black);
  text-align: right;
  width: 100%;
  box-sizing: border-box;
  padding-right: 10px;
  pointer-events: none;
  z-index: 1;
}

.chat-message-version {
  font-size: 12px;
  color: var(--black);
  margin-right: 8px;
  white-space: nowrap;
}

.chat-message-user > .chat-message-container > .chat-message-item {
  background-color: var(--second);

  &:hover {
    min-width: 0;
  }
}

.chat-input-panel {
  position: relative;
  width: 100%;
  padding: 20px;
  padding-top: 10px;
  box-sizing: border-box;
  flex-direction: column;
  border-top: var(--border-in-light);
  box-shadow: var(--card-shadow);

  .chat-input-actions {
    .chat-input-action {
      margin-bottom: 10px;
    }
  }
}

@mixin single-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.prompt-hints {
  min-height: 20px;
  width: 100%;
  max-height: 50vh;
  overflow: auto;
  display: flex;
  flex-direction: column-reverse;

  background-color: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: var(--shadow);

  .prompt-hint {
    color: var(--black);
    padding: 6px 10px;
    animation: slide-in ease 0.3s;
    cursor: pointer;
    transition: all ease 0.3s;
    border: transparent 1px solid;
    margin: 4px;
    border-radius: 8px;

    &:not(:last-child) {
      margin-top: 0;
    }

    .hint-title {
      font-size: 12px;
      font-weight: bolder;

      @include single-line();
    }

    .hint-content {
      font-size: 12px;

      @include single-line();
    }

    &-selected,
    &:hover {
      border-color: var(--primary);
    }
  }
}

.shortcut-panel {
  width: 100%;
  background-color: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: var(--shadow);
  animation: slide-in ease 0.3s;

  .shortcut-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: var(--border-in-light);

    .shortcut-panel-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--black);
    }

    .shortcut-panel-close {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--hover-color);
      }

      svg {
        width: 16px;
        height: 16px;
        fill: var(--black);
      }
    }
  }

  .shortcut-panel-content {
    padding: 12px 16px;
    max-height: 300px;
    overflow-y: auto;
  }

  .shortcut-key-list {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .shortcut-key-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid var(--border-in-light);
      border-radius: 8px;
      transition: all 0.2s;

      &:hover {
        background-color: var(--hover-color);
      }

      .shortcut-key-title {
        font-size: 14px;
        color: var(--black);
        flex: 1;
      }

      .shortcut-key-keys {
        display: flex;
        gap: 4px;
        align-items: center;

        .shortcut-key {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-width: 24px;
          height: 24px;
          padding: 0 6px;
          background-color: var(--second);
          border: 1px solid var(--border-in-light);
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          color: var(--black);
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

          span {
            line-height: 1;
          }
        }
      }
    }
  }
}

.mcp-panel {
  width: 100%;
  background-color: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: var(--shadow);
  animation: slide-in ease 0.3s;

  .mcp-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: var(--border-in-light);

    .mcp-panel-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--black);
    }

    .mcp-panel-close {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--hover-color);
      }

      svg {
        width: 16px;
        height: 16px;
        fill: var(--black);
      }
    }
  }

  .mcp-panel-content {
    padding: 12px 16px;
    max-height: 300px;
    overflow-y: auto;

    .mcp-global-toggle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      margin-bottom: 16px;
      border: 1px solid var(--border-in-light);
      border-radius: 8px;
      background-color: var(--gray-50);

      .mcp-global-toggle-info {
        flex: 1;

        .mcp-global-toggle-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--black);
          margin-bottom: 4px;
        }

        .mcp-global-toggle-desc {
          font-size: 12px;
          color: var(--black);
          opacity: 0.6;
          line-height: 1.4;
        }
      }
    }

    .mcp-panel-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 20px;
      color: var(--black);
      font-size: 14px;

      svg {
        width: 16px;
        height: 16px;
        fill: var(--primary);
      }
    }

    .mcp-panel-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: var(--black);
      font-size: 14px;
      opacity: 0.6;
    }
  }

  .mcp-client-list {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .mcp-client-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid var(--border-in-light);
      border-radius: 8px;
      transition: all 0.2s;

      &:hover {
        background-color: var(--hover-color);
      }

      .mcp-client-info {
        flex: 1;

        .mcp-client-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--black);
          margin-bottom: 4px;
        }

        .mcp-client-tools {
          font-size: 12px;
          color: var(--black);
          opacity: 0.6;
        }
      }

      .mcp-client-toggle {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;

        input {
          opacity: 0;
          width: 0;
          height: 0;

          &:checked + .toggle-slider {
            background-color: var(--primary);

            &:before {
              transform: translateX(20px);
            }
          }
        }

        .toggle-slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: 0.3s;
          border-radius: 24px;

          &:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
          }
        }
      }
    }
  }
}

.chat-input-panel-inner {
  cursor: text;
  display: flex;
  flex: 1;
  border-radius: 10px;
  border: var(--border-in-light);
}

.chat-input-panel-inner-attach {
  padding-bottom: 80px;
}

.chat-input-panel-inner:has(.chat-input:focus) {
  border: 1px solid var(--primary);
}

.chat-input {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  border: none;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.03);
  background-color: var(--white);
  color: var(--black);
  font-family: inherit;
  padding: 10px 90px 10px 14px;
  resize: none;
  outline: none;
  box-sizing: border-box;
  min-height: 68px;
}

// 输入框焦点样式由父容器处理

.chat-input-send {
  background-color: var(--primary);
  color: white;

  position: absolute;
  right: 30px;
  bottom: 32px;
}

@media only screen and (max-width: 600px) {
  .chat-input {
    font-size: 16px;
  }

  .chat-input-send {
    bottom: 30px;
  }
}

.shortcut-key-container {
  padding: 10px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.shortcut-key-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}

.shortcut-key-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  padding: 10px;
  background-color: var(--white);
}

.shortcut-key-title {
  font-size: 14px;
  color: var(--black);
}

.shortcut-key-keys {
  display: flex;
  gap: 8px;
}

.shortcut-key {
  display: flex;
  align-items: center;
  justify-content: center;
  border: var(--border-in-light);
  border-radius: 8px;
  padding: 4px;
  background-color: var(--gray);
  min-width: 32px;
}

.shortcut-key span {
  font-size: 12px;
  color: var(--black);
}

.thinking-option-selected {
  background-color: var(--primary) !important;
  color: white !important;

  .shortcut-key-title {
    color: white !important;
  }
}

.thinking-option-check {
  color: var(--primary);
  font-weight: bold;
  font-size: 16px;
}

.thinking-notice {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 6px;
  text-align: center;

  [data-theme="dark"] & {
    background-color: #2a2a2a;
    color: #fff;
  }
}

.chat-main {
  display: flex;
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  .chat-body-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100%;
  }
  .chat-side-panel {
    position: absolute;
    inset: 0;
    background: var(--white);
    overflow: hidden;
    z-index: 10;
    transform: translateX(100%);
    transition: all ease 0.3s;
    &-show {
      transform: translateX(0);
    }
  }
}
