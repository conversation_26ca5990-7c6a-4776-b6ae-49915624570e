.search-collapse {
  :global {
    .ant-collapse-item {
      border: var(--border-in-light) !important;
      border-radius: 10px !important;
      background-color: var(--white) !important;
      margin-bottom: 8px !important;
    }

    .ant-collapse-header {
      color: var(--black) !important;
      font-weight: bold !important;
      font-size: 14px !important;
      padding: 6px 12px !important;
      align-items: center !important;
      transition: all 0.3s ease !important;

      .ant-collapse-expand-icon {
        color: var(--primary) !important;
      }
    }

    .ant-collapse-content {
      background-color: transparent !important;
      border-top: 1px solid var(--border-in-light) !important;

      .ant-collapse-content-box {
        padding: 8px 12px !important;
        font-size: var(--text-sm);
        color: var(--black);
        opacity: 0.8;
      }
    }
  }
}

// Think Collapse styles 
.think-collapse {
  font-size: var(--text-sm);
  :global {
    .ant-collapse-item {
      border: var(--border-in-light) !important;
      border-radius: 10px !important;
      background-color: var(--white) !important;
      margin-bottom: 8px !important;
    }

    .ant-collapse-header {
      color: var(--black) !important;
      font-weight: bold !important;
      padding: 6px 12px !important;
      align-items: center !important;
      transition: all 0.3s ease !important;

      .ant-collapse-expand-icon {
        color: var(--primary) !important;
      }
    }

    .ant-collapse-content {
      background-color: transparent !important;
      border-top: 1px solid var(--border-in-light) !important;

      .ant-collapse-content-box {
        padding: 8px 12px !important;
        color: var(--black);
        opacity: 0.8;
      }
    }
  }
}

.think-collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.copy-think-button {
  font-size: var(--text-sm);
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }
}

.disabled {
  opacity: 0.9;
  pointer-events: none;
  
  :global {
    .ant-collapse-item {
      border: none !important;
      background-color: transparent !important;
    }
    
    .ant-collapse-header {
      padding: 6px 0px !important;
    }
  }
}
